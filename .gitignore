# Dependencies
node_modules/

# Environment variables
.env
.env.*
.env.*.local
.env.local
.env.development
.env.development.local
.env.test
.env.test.local
.env.production
.env.production.local
frontend/.env
frontend/.env.*
frontend/.env.*.local
frontend/.env.local
frontend/.env.development
frontend/.env.development.local
frontend/.env.test
frontend/.env.test.local
frontend/.env.production
frontend/.env.production.local

# Build outputs
dist/
.nuxt/
.output/

# Editor files
.vscode/
.idea/
*.sublime-*
.DS_Store

# Logs
*.log
npm-debug.log*

# Testing
coverage/
.nyc_output/
test-results/
playwright-report/
EOF