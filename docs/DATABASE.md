# Database Guide

This document provides information about the database setup and usage in this project.

## Overview

This project uses:

-   **PostgreSQL** as the database engine
-   **Drizzle ORM** for database access and schema management
-   **postgres.js** as the database driver

## Database Configuration

### Environment Variables

Database connections are configured using environment variables:

-   **Development**: `.env.local` contains the development database configuration
-   **Testing**: `.env.test` contains the test database configuration
-   **Production**: Environment variables are set in the production environment

For Docker Compose, database connection strings are hardcoded in the configuration files for simplicity.

### Development Database

The development database is configured in `docker-compose.yml` and uses the values from `.env.local`:

```yaml
db:
    image: postgres:16
    environment:
        POSTGRES_USER: postgres
        POSTGRES_PASSWORD: password123
        POSTGRES_DB: fantasyfix
    ports:
        - '5432:5432'
    volumes:
        - postgres_data:/var/lib/postgresql/data
```

### Test Database

The test database is also configured in `docker-compose.yml`:

```yaml
db_test:
    image: postgres:16
    environment:
        POSTGRES_USER: postgres
        POSTGRES_PASSWORD: password123
        POSTGRES_DB: fantasyfix_test
    ports:
        - '5433:5432'
    volumes:
        - postgres_test_data:/var/lib/postgresql/data
```

### Production Database

For production, the database connection is configured using environment variables:

-   **Managed Service**: When using a managed database service (like AWS RDS, DigitalOcean, or Coolify), set the `DATABASE_URL` environment variable to the connection string provided by the service.

-   **Connection String Format**:

    ```
    postgresql://username:password@hostname:port/database
    ```

-   **SSL**: For production databases, SSL is enabled by default in the database connection code (see `frontend/server/utils/drizzle.ts`).

## Drizzle ORM Setup

### Configuration

Drizzle is configured in the following files:

-   `frontend/config/drizzle.config.ts` - Configuration for the development database
-   `frontend/config/drizzle.test.config.ts` - Configuration for the test database

### Database Connection

The database connection is set up in `frontend/server/utils/drizzle.ts`:

```typescript
import { drizzle } from 'drizzle-orm/postgres-js';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import * as schema from '~/server/db/schema';

export const createDb = (): PostgresJsDatabase<typeof schema> => {
	const config = useRuntimeConfig();
	const connectionString = config.databaseUrl;

	// Create postgres.js client
	const client = postgres(connectionString, {
		ssl: config.env === 'production',
		max: 10,
		idle_timeout: 20,
		connect_timeout: 10,
	});

	// Create Drizzle ORM instance
	return drizzle(client, { schema });
};

export const db = createDb();
```

### Schema Definition

Database schemas are defined in the `frontend/server/db/schema` directory:

-   `auth.ts`: Authentication-related tables (user, session, account)
-   `app.ts`: Application-specific tables (userSettings)
-   `index.ts`: Combines and exports all schemas

Example schema definition:

```typescript
import { pgTable, text, timestamp, boolean } from 'drizzle-orm/pg-core';

export const user = pgTable('user', {
	id: text('id').primaryKey(),
	name: text('name').notNull(),
	email: text('email').notNull().unique(),
	emailVerified: boolean('emailVerified').notNull(),
	image: text('image'),
	createdAt: timestamp('createdAt').notNull(),
	updatedAt: timestamp('updatedAt').notNull(),
});
```

## Database Migrations

### Generating Migrations

When you make changes to your schema, generate migrations:

```bash
npm run db:generate
```

This creates SQL migration files in `frontend/server/db/migrations`.

### Applying Migrations

Apply migrations to the development database:

```bash
npm run db:migrate
```

Apply migrations to the test database:

```bash
npm run db:migrate:test
```

Note: Migrations are automatically applied to the test database when running tests.

## Database Seeding

The project includes seed files to populate your databases with initial data.

### Seeding Development Database

To seed your development database with initial data:

```bash
npm run db:seed
```

### Seeding Test Database

To seed your test database with test data:

```bash
npm run db:seed:test
```

### Using the Migration Scripts

The project includes custom migration scripts in the `frontend/scripts` directory:

```bash
# Run migrations on development database
npm run db:migrate

# Run migrations on test database
npm run db:migrate:test
```

This is particularly useful when you want to view test data in Drizzle Studio without running the tests.

## Database Visualization

You can use Drizzle Studio to visualize and manage your database:

```bash
# View development database
npm run db:studio

# View test database
npm run db:studio:test
```

## Common Database Operations

### Querying Data

```typescript
// Select all users
const users = await db.select().from(schema.user);

// Select a specific user
const user = await db.select().from(schema.user).where(eq(schema.user.id, userId));
```

### Inserting Data

```typescript
// Insert a user
const newUser = await db
	.insert(schema.user)
	.values({
		id: 'user-id',
		name: 'User Name',
		email: '<EMAIL>',
		emailVerified: true,
		createdAt: new Date(),
		updatedAt: new Date(),
	})
	.returning();
```

### Updating Data

```typescript
// Update a user
await db.update(schema.user).set({ name: 'New Name', updatedAt: new Date() }).where(eq(schema.user.id, userId));
```

### Deleting Data

```typescript
// Delete a user
await db.delete(schema.user).where(eq(schema.user.id, userId));
```

## Best Practices

1. **Use Migrations**: Always use migrations to modify the database schema.
2. **Type Safety**: Leverage TypeScript and Drizzle's type safety.
3. **Connection Management**: Close connections when they're no longer needed.
4. **Transactions**: Use transactions for operations that need to be atomic.
5. **Prepared Statements**: Use prepared statements for queries with parameters.
6. **Testing**: Test database operations with the test database.

## Troubleshooting

### Connection Issues

If you can't connect to the database:

1. Make sure the database container is running:

    ```bash
    docker compose ps
    ```

2. Check the database logs:

    ```bash
    docker compose logs db
    ```

3. Try connecting directly with psql:
    ```bash
    psql -h localhost -p 5432 -U postgres -d fantasyfix
    ```

### Migration Issues

If migrations fail:

1. Check the migration files in `frontend/server/db/migrations`.
2. Try running migrations manually with drizzle-kit:

    ```bash
    npx drizzle-kit migrate
    ```

3. Check the database state:
    ```bash
    npm run db:studio
    ```
