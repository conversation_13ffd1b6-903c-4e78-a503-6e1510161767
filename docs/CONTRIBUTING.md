# Contributing Guide

Thank you for your interest in contributing to this project! This document provides guidelines and instructions for contributing.

## Development Workflow

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/your-feature-name`
3. Make your changes
4. Run tests to ensure your changes don't break existing functionality:
   ```bash
   cd frontend
   npm run test
   npm run test:e2e
   ```
5. Commit your changes with a descriptive commit message
6. Push to your fork: `git push origin feature/your-feature-name`
7. Create a pull request

## Code Style

This project follows a specific code style to maintain consistency:

- Use ESLint and Prettier for code formatting
- Follow the existing code style in the project
- Write meaningful commit messages

You can check your code style with:
```bash
npm run lint
```

## Pull Request Process

1. Ensure your code passes all tests
2. Update documentation if necessary
3. Add a description of your changes to the pull request
4. Link any related issues in the pull request description
5. Request a review from a maintainer

## Adding New Features

When adding new features:

1. Start by discussing the feature in an issue
2. Write tests for the new feature
3. Update documentation to reflect the new feature
4. Follow the development workflow above

## Reporting Bugs

When reporting bugs:

1. Check if the bug has already been reported
2. Use the bug report template
3. Include detailed steps to reproduce the bug
4. Include information about your environment (OS, browser, etc.)
5. If possible, include screenshots or videos

## Questions

If you have any questions about contributing, please open an issue with the "question" label.
