# Docker Setup for Fantasy Fix

This document explains how to use Docker with this project for development, testing, and deployment.

## Overview

The project uses a simple multi-stage Docker build approach:

1. **Base**: Common base image with Node.js
2. **Builder**: Builds the Nuxt application
3. **Production**: Minimal image with only production dependencies and build output

## Development

To start the development environment:

```bash
docker compose up
```

This will:

-   Start the Nuxt application in development mode
-   Start a PostgreSQL database for development
-   Start a PostgreSQL database for testing

The application will be available at http://localhost:3333.

## Testing

To run E2E tests in a Docker environment:

```bash
docker compose -f docker-compose.test.yml up --build
```

This will:

-   Build the application
-   Start a test database
-   Run database migrations
-   Run E2E tests

## Production

To manually test the production build:

```bash
# Build the production image
docker build -t foundation:prod --target production .

# Run the production image
docker run -p 3333:3333 -e DATABASE_URL=postgresql://postgres:<EMAIL>:5432/fantasyfix foundation:prod
```

## CI/CD Pipeline

The GitHub Actions workflow (`ci-cd.yml`) implements a streamlined CI/CD pipeline:

1. **Test and Build Job**:

    - Runs unit tests
    - Runs E2E tests
    - Builds the Nuxt application (on main and production branches)
    - Uploads build output as an artifact (on main and production branches)

2. **Deploy Job** (only on production branch):
    - Downloads the build artifact
    - Builds the production Docker image
    - Pushes the image to GitHub Container Registry

## Docker Images

The following Docker images are produced:

-   `ghcr.io/[username]/foundation:latest` - Latest production build
-   `ghcr.io/[username]/foundation:[commit-sha]` - Production build for specific commit
-   `ghcr.io/[username]/foundation:production-[run-number]` - Production build with sequential version number

## Environment Variables

The Docker setup respects the following environment variables:

-   `NODE_ENV`: Set to `development` for development, `test` for testing, `production` for production
-   `DATABASE_URL`: Database connection string

## Troubleshooting

### Database Connection Issues

If you're having trouble connecting to the database from the Docker container:

-   For macOS/Windows: Use `host.docker.internal` as the database host
-   For Linux: Use the Docker network IP or `localhost` with network_mode: host

### Volume Permissions

If you encounter permission issues with volumes:

```bash
# Fix permissions
chmod -R 777 ./frontend/node_modules
```

### Cleaning Up

To remove all containers, volumes, and images:

```bash
# Stop all containers
docker compose down -v

# Remove all related images
docker rmi $(docker images -q foundation)
```
