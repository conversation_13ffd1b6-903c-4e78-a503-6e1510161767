# Branching Strategy

This document outlines the branching strategy used in this project.

## Branch Structure

- **feature/[name]**: Feature branches for new development
- **fix/[name]**: Bug fix branches
- **main**: Integration branch for development
- **production**: Deployment branch for production releases

## Workflow

1. **Feature Development**
   - Create a feature branch from `main`: `git checkout -b feature/new-feature main`
   - Develop and test your feature
   - Create a pull request to merge into `main`

2. **Bug Fixes**
   - Create a fix branch from `main`: `git checkout -b fix/bug-description main`
   - Fix the bug and test
   - Create a pull request to merge into `main`

3. **Integration**
   - All features and fixes are merged into `main`
   - Automated tests run on every push and pull request to `main`
   - The `main` branch should always be in a deployable state

4. **Deployment**
   - When ready to deploy to production, merge `main` into `production`
   - Create a pull request from `main` to `production`
   - After approval, merge the pull request
   - Automated deployment is triggered on merges to `production`

## CI/CD Integration

- **Pull Requests**: Tests run on all pull requests to `main` and `production`
- **Main Branch**: Tests run on all pushes to `main`
- **Production Branch**: Tests run and deployment is triggered on pushes to `production`

## Versioning

Docker images built from the `production` branch are tagged with:
- `latest`: Always points to the most recent production build
- `[commit-sha]`: Specific commit identifier
- `production-[run-number]`: Sequential version number

## Rollbacks

To roll back to a previous version:
1. Identify the commit to roll back to
2. Create a new branch from that commit: `git checkout -b rollback/description [commit-sha]`
3. Create a pull request to merge into `production`
4. After approval, merge the pull request to trigger deployment
