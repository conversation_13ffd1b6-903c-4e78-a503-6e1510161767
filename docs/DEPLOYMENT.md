# Deployment Guide

This document provides instructions for deploying the application to production.

## Production Environment

The application is designed to be deployed to a production environment with the following components:

-   Node.js server for the Nuxt application
-   PostgreSQL database
-   Environment variables for configuration

## Deployment Options

### Option 1: Traditional Hosting

1. Build the application:

    ```bash
    cd frontend
    npm run build
    ```

2. Set up environment variables on your server:

    - `NODE_ENV=production`
    - `DATABASE_URL` - PostgreSQL connection string
    - `SENTRY_DSN` - Sentry DSN for error tracking
    - `GOOGLE_CLIENT_ID` - Google OAuth client ID
    - `GOOGLE_CLIENT_SECRET` - Google OAuth client secret
    - `TWITTER_CLIENT_ID` - Twitter OAuth client ID
    - `TWITTER_CLIENT_SECRET` - Twitter OAuth client secret
    - `BETTER_AUTH_SECRET` - Secret for Better Auth
    - `BETTER_AUTH_URL` - URL for Better Auth
    - `RESEND_API_KEY` - API key for Resend email service

3. Start the application:
    ```bash
    node .output/server/index.mjs
    ```

### Option 2: Docker Deployment with Coolify

1. Configure Coolify to use the repository directly:

    - Add your GitHub repository to Coolify
    - Set the build method to "Docker Compose"
    - Set "Docker Compose Location" to `docker-compose.production.yml`
    - The service name "app" is already defined in the production compose file

2. Set the environment variables in Coolify:

    - All required environment variables are listed in `docker-compose.production.yml`
    - Coolify will detect these variables automatically
    - Fill in the actual values in the Coolify dashboard
    - Make sure to set `BETTER_AUTH_URL=https://isg0sw8swcckokcggwcw8cc0.neatpixels.io` (or your production domain)
    - For the database, use your Neon database URL

3. Configure the network settings:

    - Set the port to 80
    - Enable Traefik integration
    - Set the domain to your production domain

4. Configure Coolify to automatically deploy on changes:
    - In Coolify, enable "Auto Deploy" for your application
    - This will trigger a new build and deployment whenever changes are pushed to the production branch
    - The CI/CD pipeline will ensure that only code that passes all tests is deployed
    - The application will be available at your configured domain

### Option 3: Platform as a Service (PaaS)

The application can be deployed to platforms like Vercel, Netlify, or Heroku. Follow the platform-specific instructions for deploying a Nuxt application.

## Database Deployment

1. Create a PostgreSQL database in your production environment.

2. Apply migrations to the production database:

    ```bash
    DATABASE_URL=postgresql://user:password@host:port/database npm run db:migrate
    ```

3. (Optional) Seed the production database:
    ```bash
    DATABASE_URL=postgresql://user:password@host:port/database npm run db:seed
    ```

## Continuous Deployment

The project is set up for continuous deployment using GitHub Actions. When you push to the `deploy` branch, the application is automatically built, tested, and deployed.

See the workflow file at `.github/workflows/deploy.yml` for details.

## Monitoring

The application uses Sentry for error tracking and monitoring. Make sure to set up the `SENTRY_DSN` environment variable in your production environment.

## Backup and Recovery

It's recommended to set up regular backups of your production database. PostgreSQL provides several options for backup and recovery:

-   `pg_dump` for logical backups
-   `pg_basebackup` for physical backups
-   Point-in-time recovery (PITR) for continuous backup

Consult the PostgreSQL documentation for detailed instructions on backup and recovery.
