# Setup Guide

This document provides instructions for setting up the project for development.

## Prerequisites

-   Node.js v20 or later
-   <PERSON><PERSON> and <PERSON>er Compose
-   Git

## Installation

1. Clone the repository:

    ```bash
    git clone https://github.com/yourusername/foundation.git
    cd foundation
    ```

2. Install dependencies:

    ```bash
    cd frontend
    npm install
    ```

3. Set up environment variables:

    ```bash
    cp .env.local.example .env.local
    ```

    Edit `.env.local` and add your actual credentials and API keys.

4. Start the development database:

    ```bash
    docker compose up db -d
    ```

5. Run database migrations:

    ```bash
    npm run db:migrate
    ```

6. Seed the database (optional):

    ```bash
    npm run db:seed
    ```

7. Start the development server:

    ```bash
    npm run dev
    ```

8. Open your browser and navigate to http://localhost:3000

## Environment Variables

The project uses different environment files for different environments:

-   `.env.local` - Development environment (git-ignored)
-   `.env.test` - Base test configuration (committed to git, no secrets)
-   `.env.test.local` - Test environment overrides (git-ignored)
-   `.env.production` - Production environment

## Project Structure

The project follows a structured organization:

```
/
├── docs/                       # Documentation
├── frontend/                   # Frontend application
│   ├── config/                 # Configuration files
│   │   ├── drizzle.config.ts   # Drizzle development config
│   │   └── drizzle.test.config.ts # Drizzle test config
│   ├── scripts/                # Utility scripts
│   │   ├── migrate.ts          # Database migration script
│   │   └── seed.ts             # Database seeding script
│   ├── server/                 # Server-side code
│   │   ├── api/                # API routes
│   │   ├── db/                 # Database code
│   │   │   ├── migrations/     # Database migrations
│   │   │   ├── schema/         # Database schema
│   │   │   └── seed.ts         # Database seed
│   │   └── utils/              # Server utilities
│   └── tests/                  # Tests
│       ├── e2e/                # End-to-end tests
│       └── unit/               # Unit tests
└── docker-compose.yml          # Docker Compose configuration
```

## Docker Setup

The project uses Docker Compose for local development. The following services are defined:

-   `db` - PostgreSQL database for development
-   `db_test` - PostgreSQL database for testing

You can start all services with:

```bash
docker compose up -d
```

Or start individual services:

```bash
docker compose up db -d
```

## Database Setup

See the [Database Guide](DATABASE.md) for detailed information about the database setup.

## Testing Setup

See the [Testing Guide](TESTING.md) for detailed information about the testing setup.
