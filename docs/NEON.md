# Neon Database Integration

This project uses [Neon](https://neon.tech) as its PostgreSQL database provider with the Neon serverless driver.

## Connection

The application connects to Neon using the `@neondatabase/serverless` package and the `DATABASE_URL` environment variable in `.env.local`:

```
DATABASE_URL="postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"
```

The Neon serverless driver is integrated with Drizzle ORM using the `drizzle-orm/neon-http` adapter.

## Branching Strategy

Neon provides database branching capabilities that we use for different environments:

-   **Development**: Main branch
-   **Testing**: Test branch
-   **Production**: Production branch

## Database Operations

### Running Migrations

To apply migrations to your Neon database:

```bash
npm run db:migrate
```

### Database Data

When you create a new branch in Neon, it will contain all the data from the parent branch. This means:

-   Your development branch will have all the data from production
-   Your test branch will have all the data from production
-   No manual seeding is required as data will naturally accumulate through application usage

This approach provides a realistic environment for development and testing.

## Viewing and Managing Data

Neon provides a comprehensive web UI for managing your database:

```bash
# Open the Neon console in your browser
npm run db:console
```

Once in the Neon console:

1. Select your project
2. Navigate to the Tables tab to view and manage your database tables
3. Use the SQL Editor tab to write and execute SQL queries directly

The Neon web UI provides features like:

-   Table browsing and editing
-   SQL query execution
-   Query history
-   Schema visualization
-   Performance monitoring

## Neon Branch Management

### Creating a Branch

1. Go to the Neon console
2. Navigate to the Branches tab
3. Click "Create Branch"
4. Name your branch (e.g., "test", "production")
5. Update your environment variables with the new branch connection string

## Resources

-   [Neon Documentation](https://neon.tech/docs)
-   [Neon + Nuxt Integration Guide](https://neon.tech/docs/guides/nuxt)
-   [Neon Branching Workflow](https://neon.tech/docs/get-started-with-neon/workflow-primer)
