# Node modules and logs
node_modules
npm-debug.log
yarn-debug.log
yarn-error.log

# Git and GitHub files
.git
.github
.gitignore

# Environment variables
.env
.env.*
!.env.example

# Build output
.output
dist
.nuxt

# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS files
.DS_Store
Thumbs.db

# Test coverage
coverage
.nyc_output
playwright-report
test-results

# Docker files
Dockerfile
docker-compose*.yml
.dockerignore

# Documentation
README.md
docs
*.md
