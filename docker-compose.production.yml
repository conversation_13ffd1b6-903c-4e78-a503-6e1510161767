version: '3.8'

networks:
    coolify:
        external: true

services:
    app:
        build:
            context: .
            dockerfile: Dockerfile
            target: production
        ports:
            - '3000:3000'
        environment:
            # Core environment variables
            - NUXT_PORT=3000
            - NODE_ENV=production
            # DATABASE_URL will be provided by Coolify

            # Auth configuration
            # These values will be provided by Coolify
            # - BETTER_AUTH_SECRET
            # - BETTER_AUTH_URL
            # - GOOGLE_CLIENT_ID
            # - GOOGLE_CLIENT_SECRET
            # - TWITTER_CLIENT_ID
            # - TWITTER_CLIENT_SECRET

            # Services
            # These values will be provided by Coolify
            # - RESEND_API_KEY
            # - SENTRY_DSN
            - SENTRY_ENVIRONMENT=production
        restart: unless-stopped
        networks:
            - coolify
        labels:
            - caddy.enable=true
            - caddy=gs8wk4s40ssc8c00kcso804w.neatpixels.io
            - caddy.reverse_proxy=app:3000
