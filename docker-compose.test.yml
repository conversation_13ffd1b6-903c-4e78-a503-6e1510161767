version: '3.8'

services:
    frontend-test:
        build:
            context: .
            dockerfile: Dockerfile
            target: builder
        volumes:
            - ./frontend:/app
            - /app/node_modules
        ports:
            - '3333:3333'
        env_file:
            - ./frontend/.env.test
        environment:
            - NODE_ENV=test
            # DATABASE_URL is loaded from .env.test
        command: sh -c "npm run db:migrate:test && npm run test:e2e"
