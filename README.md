# Foundation Project

A modern web application built with Nuxt.js, PostgreSQL, and Drizzle ORM.

## Project Structure

The project follows a structured organization:

```
/
├── docs/                       # Documentation
├── frontend/                   # Nuxt.js application
│   ├── config/                 # Configuration files
│   ├── scripts/                # Utility scripts
│   ├── server/                 # Server-side code
│   │   ├── api/                # API routes
│   │   ├── db/                 # Database code
│   │   └── utils/              # Server utilities
│   └── tests/                  # Tests
├── .github/                    # GitHub Actions workflows
└── docker-compose.yml          # Docker Compose configuration
```

## Documentation

-   [Setup Guide](docs/SETUP.md) - How to set up the project for development
-   [Database Guide](docs/DATABASE.md) - Database configuration and usage
-   [Testing Guide](docs/TESTING.md) - How to run tests and test setup
-   [Deployment Guide](docs/DEPLOYMENT.md) - How to deploy the application
-   [Docker Guide](docs/DOCKER.md) - Docker setup and usage

## Quick Start

```bash
# Install dependencies
npm install

# Start development database
docker compose up db -d

# Start development server
npm run dev
```

### Docker Development

```bash
# Start the entire stack with Docker
npm run docker:dev

# Run tests in Docker
npm run docker:test
```

## Features

-   Authentication with Google and Twitter
-   Database ORM with Drizzle
-   End-to-end testing with Playwright
-   UI components with Nuxt UI

## License

[MIT](LICENSE)
