# Local environment overrides
# This file should not be committed to git
# Add your local-specific environment variables here

# Sentry Configuration
SENTRY_ENVIRONMENT="production"
SENTRY_DSN="https://<EMAIL>/4509141678096384"

# Database
DATABASE_URL="postgresql://postgres:password123@localhost:5432/fantasyfix"

# Auth
GOOGLE_CLIENT_ID="705558013125-ofric7hjtv2ndigloibuvf9jghra53k3.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="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"
TWITTER_CLIENT_ID="TUR0Z1JaM2d3UHA5VHNEaHNBUnk6MTpjaQ"
TWITTER_CLIENT_SECRET="d61ezTFT8lZx-gomOlMJcZtUgHHZr0tDaYJ2tYvsCufpq8CU4T"
BETTER_AUTH_SECRET="fsOHHX7nz2F4F8KqgCNXimWnAMXW3R9S"
BETTER_AUTH_URL="https://foundation.neatpixels.io"

# Email
RESEND_API_KEY="re_CnycQE9j_5pF8vUzDKpYqLmnR3rEJwUki"