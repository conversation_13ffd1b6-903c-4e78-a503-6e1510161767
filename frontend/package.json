{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev --host --dotenv .env.local", "dev:test": "nuxt dev --host --port 3333 --dotenv .env.test", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "db:generate": "drizzle-kit generate --config server/utils/drizzle-config.ts", "db:migrate": "tsx server/utils/run-migrations.ts", "db:migrate:test": "tsx server/utils/run-migrations.ts --test", "db:migrate:production": "tsx server/utils/run-migrations.ts --production", "db:console": "open https://console.neon.tech", "test": "vitest", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:e2e:ci": "playwright test --workers=1 --reporter=github,list --trace=off", "test:e2e:update-schema": "npx tsx tests/e2e/db-setup.ts && npm run test:e2e"}, "dependencies": {"@neondatabase/serverless": "^1.0.0", "@nuxt/image": "^1.8.1", "@nuxt/ui": "^3.1.2", "@radix-icons/vue": "^1.0.0", "@vee-validate/zod": "^4.14.7", "@vueuse/core": "^11.1.0", "better-auth": "^1.0.13", "clsx": "^2.1.1", "drizzle-orm": "^0.43.1", "nuxt": "^3.13.0", "pg": "^8.15.6", "postgres": "^3.4.5", "resend": "^3.0.0", "uuid": "^11.1.0", "vee-validate": "^4.14.7", "vue": "latest", "vue-router": "latest", "zod": "^3.23.8"}, "devDependencies": {"@iconify-json/lucide": "^1.2.35", "@nuxt/test-utils": "^3.17.2", "@playwright/test": "^1.42.1", "@sentry/node": "^9.11.0", "@sentry/profiling-node": "^9.11.0", "@sentry/types": "^9.11.0", "@sentry/vue": "^9.11.0", "@types/uuid": "^10.0.0", "@vitejs/plugin-vue": "^5.2.3", "@vitest/coverage-v8": "^1.3.1", "@vitest/ui": "^1.3.1", "@vue/test-utils": "^2.4.4", "dotenv": "^16.4.5", "drizzle-kit": "^0.31.1", "happy-dom": "^17.4.4", "playwright-core": "^1.52.0", "tsx": "^4.19.4", "vite": "^5.4.18", "vite-svg-loader": "^5.1.0", "vitest": "^1.6.1", "wait-on": "^7.2.0"}}