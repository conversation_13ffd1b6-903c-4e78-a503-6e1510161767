import { loadTestEnv } from './utils/env-loader.js';
import { runMigrations } from '../../server/utils/migrations.js';

// Load test environment variables
loadTestEnv();

/**
 * Updates the test database schema by running migrations
 * No seeding or database creation needed since we're using a permanent Neon test branch
 */
async function setupTestDatabase() {
	try {
		console.log('Updating test database schema...');

		// Run migrations to ensure schema is up to date
		await runMigrations({ environment: 'test' });

		console.log('Using existing data from permanent Neon test branch');
	} catch (error) {
		console.error('Error updating test database schema:', error);
		throw error;
	}
}

// Run setup if this file is executed directly
// In ES modules, there's no direct equivalent to require.main === module
// Instead, we can check if this file is being executed directly
// This will be true when running the file directly, but false when imported
if (import.meta.url === `file://${process.argv[1]}`) {
	setupTestDatabase().catch(console.error);
}

export { setupTestDatabase };
