import { test } from '@nuxt/test-utils/playwright';
import { expectUserLoggedOut, waitForPageLoad } from './helpers.js';

test.describe('Authentication', () => {
	test('shows login message when not authenticated', async ({ page, goto }) => {
		// Set a longer timeout for this test in CI
		test.slow();

		// Navigate to the home page
		await goto('/');

		// Wait for the page to be fully loaded
		await waitForPageLoad(page);

		// Use the helper to check logged out state
		await expectUserLoggedOut(page);
	});
});
