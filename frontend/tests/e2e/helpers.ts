/**
 * E2E test helpers
 *
 * This file contains helper functions for E2E tests to make them more maintainable
 * and consistent across the test suite.
 */
import { expect } from '@nuxt/test-utils/playwright';
import type { Page } from '@playwright/test';

/**
 * Waits for the page to be fully loaded and hydrated
 * @param page Playwright page object
 */
export async function waitForPageLoad(page: Page): Promise<void> {
	// Wait for Nuxt hydration to complete
	await page.waitForFunction(
		() => {
			// @ts-ignore - __NUXT__ is added by Nuxt.js
			return window.__NUXT__ !== undefined;
		},
		{ timeout: process.env.CI ? 60000 : 30000 }
	);
}

/**
 * Logs in a test user
 * @param page Playwright page object
 * @param email User email
 * @param password User password
 */
export async function loginTestUser(page: Page, email = '<EMAIL>', password = 'password123'): Promise<void> {
	// Navigate to login page
	await page.goto('/login');

	// Fill in login form
	await page.getByLabel('Email').fill(email);
	await page.getByLabel('Password').fill(password);

	// Submit form
	await page.getByRole('button', { name: 'Sign in' }).click();

	// Wait for navigation to complete
	await page.waitForURL('/**');
}

/**
 * Logs out the current user
 * @param page Playwright page object
 */
export async function logoutUser(page: Page): Promise<void> {
	// Click on user menu
	await page.getByRole('button', { name: 'User menu' }).click();

	// Click logout
	await page.getByRole('menuitem', { name: 'Sign out' }).click();

	// Wait for navigation to complete
	await page.waitForURL('/login');
}

/**
 * Asserts that the user is logged in
 * @param page Playwright page object
 */
export async function expectUserLoggedIn(page: Page): Promise<void> {
	// Check for user menu
	await expect(page.getByRole('button', { name: 'User menu' })).toBeVisible();
}

/**
 * Asserts that the user is logged out
 * @param page Playwright page object
 */
export async function expectUserLoggedOut(page: Page): Promise<void> {
	// Check for login button or sign in text
	await expect(page.getByText('Please sign in')).toBeVisible();
}
