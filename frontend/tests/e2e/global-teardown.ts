/**
 * Global teardown for Playwright tests
 *
 * This file is executed after all tests have completed.
 * It's used to clean up resources that were created during testing.
 */

export default async function globalTeardown() {
	console.log('Global teardown - cleaning up after tests');

	// You can add cleanup logic here if needed
	// For example, closing database connections, stopping servers, etc.
}
