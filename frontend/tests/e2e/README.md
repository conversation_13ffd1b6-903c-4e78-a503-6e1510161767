# E2E Testing Guide

This document provides an overview of the E2E testing setup for the project.

## Overview

We use <PERSON><PERSON> for E2E testing, which allows us to test the application in real browsers. Our tests are designed to run both locally and in CI environments.

## Setup

### Prerequisites

-   Node.js 20+
-   npm
-   Docker (for local database testing)

### Local Development

1. Start the test database:

    ```bash
    docker compose up db_test -d
    ```

2. Run the migrations:

    ```bash
    npm run db:migrate:test
    ```

3. Run the tests:
    ```bash
    npm run test:e2e
    ```

### CI Environment

In CI, the tests run automatically as part of the GitHub Actions workflow. The workflow:

1. Sets up a PostgreSQL service container
2. Runs migrations and seeds the database
3. Installs Chromium Headless Shell (faster than full Chromium)
4. Runs the tests with optimized settings

## Test Structure

-   **Global Setup**: `tests/e2e/global-setup.ts` - Runs before all tests
-   **Global Teardown**: `tests/e2e/global-teardown.ts` - Runs after all tests
-   **Helpers**: `tests/e2e/helpers.ts` - Common test helpers
-   **Test Files**: `tests/e2e/*.spec.ts` - The actual test files

## Best Practices

1. **Use Helpers**: Use the helper functions in `helpers.ts` for common operations
2. **Group Tests**: Use `test.describe()` to group related tests
3. **Isolation**: Each test should be independent and not rely on state from other tests
4. **Timeouts**: Use longer timeouts in CI environments
5. **Assertions**: Make assertions specific and meaningful

## Commands

-   `npm run test:e2e` - Run all E2E tests using Chromium
-   `npm run test:e2e:headed` - Run tests with visible browser
-   `npm run test:e2e:debug` - Run tests in debug mode
-   `npm run test:e2e:ui` - Run tests with Playwright UI
-   `npm run test:e2e:ci` - Run tests with CI-optimized settings

## Troubleshooting

### Common Issues

1. **Database Connection Errors**: Make sure the test database is running
2. **Timeout Errors**: Increase timeouts for slow operations
3. **Selector Errors**: Check if the element structure has changed

### Debugging Tips

1. Use `test:e2e:headed` to see the browser during test execution
2. Use `test:e2e:debug` to pause execution and inspect the state
3. Check the test reports in `playwright-report/`
