import { config } from 'dotenv';
import * as fs from 'fs';
import * as path from 'path';

/**
 * Loads environment variables for testing
 * @returns The loaded environment variables
 */
export function loadTestEnv() {
	// Load base test env vars
	config({ path: '.env.test' });

	// Load local overrides if they exist
	const localEnvPath = path.resolve(process.cwd(), '.env.test.local');
	if (fs.existsSync(localEnvPath)) {
		config({ path: localEnvPath, override: true });
		console.log('Loaded test environment variables from .env.test.local');
	} else {
		console.warn('No .env.test.local file found. Make sure to create it with your Neon test branch connection string.');
	}

	return process.env;
}

/**
 * Converts process.env to a Record<string, string> for Playwright
 */
export function getEnvRecord(): Record<string, string> {
	return Object.entries(process.env).reduce((acc, [key, value]) => {
		if (value !== undefined) {
			acc[key] = value;
		}
		return acc;
	}, {} as Record<string, string>);
}
