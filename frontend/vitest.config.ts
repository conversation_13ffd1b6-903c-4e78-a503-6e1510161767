import { defineVitestConfig } from '@nuxt/test-utils/config';

export default defineVitestConfig({
	test: {
		environment: 'nuxt',
		exclude: [
			'**/node_modules/**',
			'**/dist/**',
			'**/server/db/seed*.ts',
			'**/scripts/**',
			'**/tests/e2e/**', // Explicitly exclude all e2e tests
			'**/*.spec.ts', // Exclude Playwright spec files
		],
		include: [
			// Component tests
			'**/components/**/*.test.ts',
			// Other unit tests
			'**/tests/unit/**/*.test.ts',
			'**/*.unit.test.ts',
		],
		coverage: {
			provider: 'v8',
			reporter: ['text', 'json', 'html'],
			exclude: [
				// Test files
				'**/*.test.ts',
				'**/*.spec.ts',
				'**/tests/**',

				// Build and config files
				'**/node_modules/**',
				'**/dist/**',
				'**/.nuxt/**',
				'**/coverage/**',
				'**/*.config.ts',
				'**/*.config.js',
				'**/nuxt.config.ts',
				'**/playwright.config.ts',
				'**/config/**',

				// Server and utility files
				'**/server/db/migrations/**',
				'**/server/db/seed*.ts',
				'**/server/db/schema/**',
				'**/server/plugins/**',
				'**/server/utils/**',
				'**/server/api/**',
				'**/scripts/**',

				// Generated reports
				'**/playwright-report/**',

				// Auth schema
				'**/auth-schema.ts',

				// Other non-testable files
				'**/*.d.ts',
				'**/types/**',
				'**/mocks/**',
				'**/public/**',
				'**/assets/**',
				'**/lib/**',
			],
			// Only include source files that can be tested
			include: ['**/components/**', '**/composables/**', '**/utils/**', '**/stores/**', '**/pages/**', '**/layouts/**'],
			// Aim for good coverage thresholds
			thresholds: {
				lines: 70,
				functions: 70,
				branches: 60,
				statements: 70,
			},
		},
	},
});
