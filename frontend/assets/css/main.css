@import 'tailwindcss';
@import '@nuxt/ui';

body {
	text-rendering: optimizeLegibility;
	-webkit-font-smoothing: antialiased;
}

@theme {
	--font-family-sans: 'Inter', sans-serif;
}

:root {
	--ui-radius: var(0.5rem);
}

@layer utilities {
	.border-dimmed {
		border-color: var(--ui-color-neutral-200);

		@variant dark {
			border-color: var(--ui-color-neutral-800);
		}
	}
}

.dark {
}

@layer base {
	::placeholder {
		color: var(--ui-text-dimmed);
	}
}
