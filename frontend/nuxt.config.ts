// nuxt.config.ts
import svgLoader from 'vite-svg-loader';

export default defineNuxtConfig({
	modules: ['@nuxt/ui', '@nuxt/image', '@nuxt/test-utils/module'],

	vite: {
		plugins: [svgLoader()],
	},

	imports: {
		dirs: ['types/*.ts', 'store/*.ts', 'types/**/*.ts'],
	},

	css: ['~/assets/css/main.css'],

	ui: {
		theme: {
			colors: ['primary', 'error'],
		},
	},

	icon: {
		customCollections: [
			{
				prefix: 'foundation',
				dir: './assets/icons',
			},
		],
	},

	colorMode: {
		classSuffix: '',
		preference: 'system',
	},

	compatibilityDate: '2024-11-17',

	runtimeConfig: {
		// The private keys which are only available within server-side
		apiSecret: '123',
		// Database configuration
		databaseUrl: process.env.DATABASE_URL,
		// Auth configuration
		googleClientId: process.env.GOOGLE_CLIENT_ID,
		googleClientSecret: process.env.GOOGLE_CLIENT_SECRET,
		twitterClientId: process.env.TWITTER_CLIENT_ID,
		twitterClientSecret: process.env.TWITTER_CLIENT_SECRET,
		resendApiKey: process.env.RESEND_API_KEY,
		// Keys within public, will be also exposed to the client-side
		public: {
			env: process.env.NODE_ENV || 'env',
			sentry: {
				dsn: process.env.SENTRY_DSN,
				environment: process.env.SENTRY_ENVIRONMENT || 'development',
			},
		},
	},

	devtools: { enabled: false },

	future: {
		compatibilityVersion: 4,
	},

	image: {
		domains: ['images.unsplash.com'],
	},
});
