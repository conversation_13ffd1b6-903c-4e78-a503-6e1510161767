<template>
	<NuxtLayout name="auth">
		<div class="space-y-6 sm:space-y-10 -mt-4 md:mt-0">
			<div class="text-center space-y-4">
				<Logo class="size-12 inline-block" />
				<h1 class="text-xl md:text-2xl font-medium">Sign up to Foundation</h1>
			</div>
			<!-- Error <PERSON> -->
			<UAlert
				v-if="formError"
				color="error"
				variant="soft"
				:description="formError"
				:close="{ size: 'md', color: 'error', variant: 'link' }"
				@update:open="handleAlertClose"
				icon="i-lucide-circle-alert" />
			<!-- Form -->
			<UForm
				:schema="schema"
				:state="state"
				class="space-y-6"
				@submit="handleSignUp">
				<div class="space-y-4">
					<UFormField
						label="Name"
						name="name"
						:ui="{ label: 'sr-only' }">
						<UInput
							v-model="state.name"
							placeholder="Name"
							class="w-full"
							variant="subtle"
							size="lg" />
					</UFormField>
					<UFormField
						label="Email"
						name="email"
						:ui="{ label: 'sr-only' }">
						<UInput
							v-model="state.email"
							placeholder="Email address"
							class="w-full"
							variant="subtle"
							size="lg" />
					</UFormField>
					<UFormField
						label="Password"
						name="password"
						:ui="{ label: 'sr-only' }">
						<UInput
							v-model="state.password"
							placeholder="Password"
							type="password"
							class="w-full"
							variant="subtle"
							size="lg" />
					</UFormField>
				</div>
				<UButton
					block
					size="xl"
					class="text-sm py-3"
					type="submit">
					Sign Up with Email
				</UButton>
			</UForm>
		</div>
		<template #footer>
			<NuxtLink
				to="/sign-in"
				class="text-sm text-[var(--ui-text-muted)] hover:text-[var(--ui-text)]">
				Already have an account?
			</NuxtLink>
		</template>
	</NuxtLayout>
</template>

<script lang="ts" setup>
import { signUp } from '~/server/utils/auth-client.js';
import Logo from '@/assets/img/logo.svg?component';
import * as z from 'zod';
import type { FormSubmitEvent } from '#ui/types';
import type { ErrorContext } from '~/types/auth';

definePageMeta({
	title: 'Sign Up',
	layout: false,
});

// Schema validation using Zod
const schema = z.object({
	name: z.string().min(2, 'Must be at least 2 characters'),
	email: z.string().email('Invalid email'),
	password: z.string().min(8, 'Must be at least 8 characters'),
});

// Type for the form schema
type Schema = z.infer<typeof schema>;

// Reactive state for form data
const state = reactive<Partial<Schema>>({
	name: undefined,
	email: undefined,
	password: undefined,
});

// Error message for the alert
const formError = ref<string | undefined>(undefined);

// Handle alert close event
const handleAlertClose = (openStatus: boolean): void => {
	if (!openStatus) {
		formError.value = '';
	}
};

// Handle form submission
const handleSignUp = async (event: FormSubmitEvent<Schema>): Promise<void> => {
	await signUp.email(
		{
			name: event.data.name,
			email: event.data.email,
			password: event.data.password,
			callbackURL: '/verify',
		},
		{
			onError(context: ErrorContext) {
				formError.value = context.error.message; // Display the error in the alert
			},
			onSuccess() {
				navigateTo('/');
			},
		}
	);
};
</script>

<style></style>
