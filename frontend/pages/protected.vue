<script lang="ts" setup>
import { signOut, useAuthSession } from '~~/server/utils/auth-client';
const router = useRouter();

const HandleSingOut = async () => {
	await signOut();
	router.push('/login');
};

const { data: session, isPending, error } = await useAuthSession(useFetch);
</script>

<template>
	<div>
		<UButton
			class="px-5 py-3 border border-slate-400 rounded-lg"
			@click="HandleSingOut">
			Logout
		</UButton>
		<section>Pending: {{ isPending }}</section>
		<section>Error: {{ error }}</section>
		<section>
			User:
			<code>{{ session?.user }}</code>
		</section>
	</div>
</template>
<style scoped></style>
