<template>
	<NuxtLayout name="auth">
		<div class="space-y-6 sm:space-y-10 -mt-4 md:mt-0">
			<div class="text-center space-y-4">
				<Logo class="size-12 inline-block" />
				<h1 class="text-xl md:text-2xl font-medium">Reset your password</h1>
				<p>Enter your email address below, and we'll send you instructions on how to reset your password.</p>
			</div>
			<!-- Error <PERSON>ert -->
			<UAlert
				v-if="formError"
				color="error"
				variant="soft"
				:description="formError"
				:close="{ size: 'md', color: 'error', variant: 'link' }"
				@update:open="handleAlertClose"
				icon="i-lucide-circle-alert" />
			<!-- Form -->
			<UForm
				:schema="schema"
				:state="state"
				class="space-y-6"
				@submit="handleResetPassword">
				<div class="space-y-4">
					<UFormField
						label="Password"
						name="password"
						:ui="{ label: 'sr-only' }">
						<UInput
							v-model="state.password"
							placeholder="Enter new password"
							type="password"
							class="w-full"
							variant="subtle"
							size="lg" />
					</UFormField>
					<UFormField
						label="Confirm Password"
						name="confirmPassword"
						:ui="{ label: 'sr-only' }">
						<UInput
							v-model="state.confirmPassword"
							placeholder="Confirm new password"
							type="password"
							class="w-full"
							variant="subtle"
							size="lg" />
					</UFormField>
				</div>
				<UButton
					block
					size="xl"
					class="text-sm py-3"
					type="submit">
					Update Password
				</UButton>
			</UForm>
		</div>
		<template #footer>
			<NuxtLink
				to="/sign-in"
				class="text-sm text-[var(--ui-text-muted)] hover:text-[var(--ui-text)]">
				Back to Sign In
			</NuxtLink>
		</template>
	</NuxtLayout>
</template>

<script lang="ts" setup>
import { resetPassword } from '~/server/utils/auth-client.js';
import Logo from '@/assets/img/logo.svg?component';
import * as z from 'zod';
import type { FormSubmitEvent } from '#ui/types';
import type { ErrorContext } from '~/types/auth';

definePageMeta({
	title: 'Reset Password',
	layout: false,
});

// Schema validation using Zod
const schema = z
	.object({
		password: z.string().min(8, 'Must be at least 8 characters'),
		confirmPassword: z.string().min(8, 'Must be at least 8 characters'),
	})
	.refine((data) => data.password === data.confirmPassword, {
		message: 'Passwords do not match',
		path: ['confirmPassword'],
	});

// Type for the form schema
type Schema = z.infer<typeof schema>;

// Reactive state for form data
const state = reactive<Partial<Schema>>({
	password: undefined,
	confirmPassword: undefined,
});

// Error message for the alert
const formError = ref<string | undefined>(undefined);

// Handle alert close event
const handleAlertClose = (openStatus: boolean): void => {
	if (!openStatus) {
		formError.value = '';
	}
};

// Handle form submission
const handleResetPassword = async (event: FormSubmitEvent<Schema>): Promise<void> => {
	await resetPassword(
		{
			newPassword: event.data.password,
		},
		{
			onError(context: ErrorContext) {
				formError.value = context.error.message;
			},
			onSuccess() {
				navigateTo('/sign-in');
			},
		}
	);
};
</script>

<style></style>
