<template>
	<NuxtLayout name="auth">
		<div class="space-y-6 sm:space-y-10 -mt-4 md:mt-0">
			<div class="text-center space-y-4">
				<Logo class="size-12 inline-block" />
				<h1 class="text-xl md:text-2xl font-medium">Enter your email below to reset your password</h1>
			</div>
			<!-- Error <PERSON> -->
			<UAlert
				v-if="formError"
				color="error"
				variant="soft"
				:description="formError"
				:close="{ size: 'md', color: 'error', variant: 'link' }"
				@update:open="handleAlertClose"
				icon="i-lucide-circle-alert" />
			<!-- Form -->
			<div v-if="resetRequested">
				We've sent you an email which you can use to reset your password. Check your spam folder if you haven't received it after a few minutes.
			</div>
			<UForm
				v-else
				:schema="schema"
				:state="state"
				class="space-y-6"
				@submit="handleForgetPassword">
				<div class="space-y-4">
					<UFormField
						label="Email"
						name="email"
						:ui="{ label: 'sr-only' }">
						<UInput
							v-model="state.email"
							placeholder="Email address"
							class="w-full"
							variant="subtle"
							size="lg" />
					</UFormField>
				</div>
				<UButton
					block
					size="xl"
					class="text-sm py-3"
					type="submit">
					Reset Password
				</UButton>
			</UForm>
		</div>
		<template #footer>
			<NuxtLink
				to="/sign-in"
				class="text-sm text-[var(--ui-text-muted)] hover:text-[var(--ui-text)]">
				Back to Sign In
			</NuxtLink>
		</template>
	</NuxtLayout>
</template>

<script lang="ts" setup>
import { forgetPassword } from '~/server/utils/auth-client.js';
import Logo from '@/assets/img/logo.svg?component';
import * as z from 'zod';
import type { FormSubmitEvent } from '#ui/types';
import type { ErrorContext } from '~/types/auth';

definePageMeta({
	title: 'Forgotten Password',
	layout: false,
});

// Schema validation using Zod
const schema = z.object({
	email: z.string().email('Invalid email'),
});

// Type for the form schema
type Schema = z.infer<typeof schema>;

// Reactive state for form data
const state = reactive<Partial<Schema>>({
	email: undefined,
});

// Error message for the alert
const formError = ref<string | undefined>(undefined);

// Handle alert close event
const handleAlertClose = (openStatus: boolean): void => {
	if (!openStatus) {
		formError.value = '';
	}
};

// Reset password request status
const resetRequested = ref(false);

// Handle form submission
const handleForgetPassword = async (event: FormSubmitEvent<Schema>): Promise<void> => {
	await forgetPassword(
		{
			email: event.data.email,
			redirectTo: '/reset-password',
		},
		{
			onError(context: ErrorContext) {
				formError.value = context.error.message; // Display the error in the alert
			},
			onSuccess() {
				resetRequested.value = true;
			},
		}
	);
};
</script>

<style></style>
