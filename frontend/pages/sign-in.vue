<template>
	<NuxtLayout name="auth">
		<div class="space-y-6 sm:space-y-10 -mt-4 md:mt-0">
			<!-- Header -->
			<div class="text-center space-y-4">
				<Logo class="size-12 inline-block" />
				<h1 class="text-xl md:text-2xl font-medium">Sign in to Foundation</h1>
			</div>

			<!-- Social Auth Buttons -->
			<div class="sm:flex space-y-2 sm:space-y-0 gap-4 sm:h-12">
				<UButton
					v-for="provider in socialProviders"
					:key="provider.id"
					variant="soft"
					color="neutral"
					size="lg"
					block
					type="button"
					@click="signInWithSocial(provider.id)">
					<UIcon
						:name="provider.icon"
						class="size-6" />
					{{ provider.label }}
				</UButton>
			</div>

			<!-- Divider -->
			<USeparator
				label="Or"
				:ui="{ label: 'text-[var(--ui-text-muted)]' }" />

			<!-- Error <PERSON> -->
			<UAlert
				v-if="formError"
				color="error"
				variant="soft"
				:description="formError"
				:close="{ size: 'md', color: 'error', variant: 'link' }"
				@update:open="handleAlertClose"
				icon="i-lucide-circle-alert" />

			<!-- Form -->
			<UForm
				:schema="schema"
				:state="state"
				class="space-y-6"
				@submit="handleSignIn">
				<div class="space-y-4">
					<UFormField
						label="Email"
						name="email"
						:ui="{ label: 'sr-only' }">
						<UInput
							v-model="state.email"
							placeholder="Email address"
							class="w-full"
							variant="subtle"
							size="lg" />
					</UFormField>
					<UFormField
						label="Password"
						name="password"
						:ui="{ label: 'sr-only' }">
						<UInput
							v-model="state.password"
							placeholder="Password"
							type="password"
							class="w-full"
							variant="subtle"
							size="lg" />
					</UFormField>
				</div>
				<UButton
					block
					size="xl"
					class="text-sm py-3"
					type="submit">
					Sign In
				</UButton>
			</UForm>
		</div>
		<template #footer>
			<NuxtLink
				to="/forgotten-password"
				class="text-sm text-[var(--ui-text-muted)] hover:text-[var(--ui-text)]">
				Forgotten password?
			</NuxtLink>
			<NuxtLink
				to="/sign-up"
				class="text-sm text-[var(--ui-text-muted)] hover:text-[var(--ui-text)] ml-auto">
				Sign up for an account
			</NuxtLink>
		</template>
	</NuxtLayout>
</template>

<script lang="ts" setup>
import { signIn } from '~/server/utils/auth-client.js';
import Logo from '@/assets/img/logo.svg?component';
import type { FormSubmitEvent } from '#ui/types';
import * as z from 'zod';
import type { SocialProviderList } from 'better-auth/social-providers';
import type { ErrorContext } from '~/types/auth';

// Page metadata
definePageMeta({
	title: 'Sign In',
	layout: false,
});

type SocialProvider = SocialProviderList[number];

// Social sign-in providers
interface SocialProviderData {
	id: SocialProvider;
	icon: string;
	label: string;
}

const socialProviders: SocialProviderData[] = [
	{ id: 'google', icon: 'i-foundation-google', label: 'Google' },
	{ id: 'twitter', icon: 'i-foundation-twitter', label: 'Twitter' },
];

// Schema validation using Zod
const schema = z.object({
	email: z.string().email('Invalid email'),
	password: z.string().min(8, 'Must be at least 8 characters'),
});

// Reactive state for form data
const state = reactive<Partial<z.infer<typeof schema>>>({
	email: undefined,
	password: undefined,
});

// Error state
const formError = ref<string | undefined>(undefined);

// Handle alert close
function handleAlertClose(openState: boolean): void {
	if (!openState) formError.value = '';
}

// Handle form submission
async function handleSignIn(event: FormSubmitEvent<z.infer<typeof schema>>): Promise<void> {
	await signIn.email(
		{
			email: event.data.email,
			password: event.data.password,
			callbackURL: '/',
		},
		{
			onError(context: ErrorContext) {
				formError.value = context.error.message;
			},
		}
	);
}

// Social sign-in handler
async function signInWithSocial(provider: SocialProvider): Promise<void> {
	try {
		console.log(`Attempting to sign in with ${provider}`);

		await signIn.social({
			provider,
			callbackURL: '/',
		});

		console.log(`Social sign-in initiated for ${provider}`);
	} catch (error) {
		console.error(`Social sign-in error with ${provider}:`, error);

		if (error instanceof Error) {
			formError.value = error.message;
		} else {
			formError.value = 'An unknown error occurred.';
		}
	}
}
</script>
