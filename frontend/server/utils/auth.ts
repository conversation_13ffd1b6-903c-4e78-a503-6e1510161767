import { betterAuth } from 'better-auth';
import type { User } from 'better-auth';
import { drizzleAdapter } from 'better-auth/adapters/drizzle';
import { db, schema } from './drizzle';
import { createAuthMiddleware } from 'better-auth/plugins';

const config = useRuntimeConfig();

// Debug logging for social provider configuration
console.log('Auth Configuration - Social Providers:');
console.log('Google Client ID exists:', !!config.googleClientId);
console.log('Google Client Secret exists:', !!config.googleClientSecret);
console.log('Twitter Client ID exists:', !!config.twitterClientId);
console.log('Twitter Client Secret exists:', !!config.twitterClientSecret);

// Prepare social providers configuration
const socialProviders: Record<string, any> = {};

// Add Google provider if credentials exist
if (config.googleClientId && config.googleClientSecret) {
	socialProviders.google = {
		clientId: config.googleClientId,
		clientSecret: config.googleClientSecret,
	};
	console.log('Google provider configured successfully');
}

// Add Twitter provider if credentials exist
if (config.twitterClientId && config.twitterClientSecret) {
	socialProviders.twitter = {
		clientId: config.twitterClientId,
		clientSecret: config.twitterClientSecret,
	};
	console.log('Twitter provider configured successfully');
}

export const auth = betterAuth({
	database: drizzleAdapter(db, {
		provider: 'pg',
		schema: schema,
	}),
	socialProviders,
	emailAndPassword: {
		enabled: true,
		requireEmailVerification: false,
		async sendResetPassword({ user, url, token }: { user: User; url: string; token: string }, request?: Request) {
			try {
				// Call your API route to send the email
				await $fetch('/api/send', {
					method: 'POST',
					body: {
						to: user.email,
						subject: 'Reset Your Password',
						html: `
					<p>Hello ${user.name || user.email},</p>
					<p>Click the link below to reset your password:</p>
					<a href="${url}">${url}</a>
					<p>If you didn't request this, you can ignore this email.</p>
				  `,
					},
				});
			} catch (error) {
				console.error('Failed to send reset password email:', error);
				throw new Error('Unable to send reset password email.');
			}
		},
	},
	emailVerification: {
		async sendVerificationEmail({ user, url, token }: { user: User; url: string; token: string }, request?: Request) {
			try {
				// Call your API route to send the email
				await $fetch('/api/send', {
					method: 'POST',
					body: {
						to: user.email,
						subject: 'Verify your email address',
						html: `
					<p>Hello ${user.name || user.email},</p>
					<p>Click the link below to verify your email:</p>
					<a href="${url}">${url}</a>
					<p>If you didn't request this, you can ignore this email.</p>
				  `,
					},
				});
			} catch (error) {
				console.error('Failed to send email verification:', error);
				throw new Error('Unable to send verification email.');
			}
		},
		autoSignInAfterVerification: true,
		sendOnSignUp: true,
	},
	security: {
		passwordPolicy: {
			minLength: 8,
			requireNumbers: true,
			requireSpecialChars: true,
			requireUppercase: true,
		},
		session: {
			maxAge: 30 * 24 * 60 * 60, // 30 days
			updateAge: 24 * 60 * 60, // 24 hours
		},
	},
	hooks: {
		after: createAuthMiddleware(async (ctx) => {
			if (ctx.path === '/get-session') {
				if (!ctx.context.session) {
					return ctx.json({
						session: null,
						user: null,
					});
				}
				return ctx.json(ctx.context.session);
			}
		}),
	},
});
