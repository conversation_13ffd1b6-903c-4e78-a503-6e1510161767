/**
 * Unified Drizzle configuration
 *
 * This module provides a function to generate Drizzle configuration for different environments.
 */

import type { Config } from 'drizzle-kit';
import * as dotenv from 'dotenv';
import * as path from 'path';
import { MigrationEnvironment } from './migrations';

/**
 * Generate Drizzle configuration for the specified environment
 * @param environment The environment to generate configuration for
 * @returns Drizzle configuration
 */
export function getDrizzleConfig(environment: MigrationEnvironment = 'development'): Config {
	// Load environment variables from the appropriate files
	if (environment === 'test') {
		// For test environment, try .env.test.local first, then .env.test
		dotenv.config({ path: '.env.test.local' });
		// If DATABASE_URL is still not set, try .env.test (for CI)
		if (!process.env.DATABASE_URL) {
			dotenv.config({ path: '.env.test' });
		}
	} else {
		// For development, use .env.local
		dotenv.config({ path: '.env.local' });
	}

	// Return Drizzle configuration
	return {
		schema: [
			// Explicitly list schema files to avoid import resolution issues
			path.resolve('./server/db/schema/auth.ts'),
			path.resolve('./server/db/schema/app.ts'),
		],
		out: './server/db/migrations',
		dialect: 'postgresql',
		verbose: true,
		strict: true,
		dbCredentials: {
			url: process.env.DATABASE_URL || '',
		},
	} as Config;
}

// Default configuration for development environment
export default getDrizzleConfig('development');
