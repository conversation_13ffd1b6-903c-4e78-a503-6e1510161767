import { drizzle } from 'drizzle-orm/neon-http';
import { neon } from '@neondatabase/serverless';
import * as schema from '~/server/db/schema'; // Import all schema files

/**
 * Creates a database connection with Drizzle ORM using Neon serverless driver
 * @returns DrizzleNeonDatabase instance
 */
export const createDb = () => {
	// Get connection string from Nuxt runtime config or environment variables
	const connectionString = process.env.DATABASE_URL || (typeof useRuntimeConfig === 'function' ? useRuntimeConfig().databaseUrl : '');

	if (!connectionString) {
		throw new Error('DATABASE_URL environment variable is not set');
	}

	// Create Drizzle ORM instance with Neon HTTP client
	return drizzle(neon(connectionString), { schema });
};

// Create a singleton database instance
export const db = createDb();

// Re-export the schema for convenience
export { schema };
