import { defineEvent<PERSON><PERSON><PERSON>, getQuery } from 'h3';

// Define types for mail piece details
interface InternationalPostalProvider {
	url: string;
	title: string;
	description: string;
}

interface Event {
	eventCode: string;
	eventName: string;
	eventDateTime: string;
	locationName: string;
}

interface Link {
	href: string;
	title: string;
	description: string;
}

interface MailPiece {
	mailPieceId: string;
	carrierShortName: string;
	carrierFullName: string;
	summary: {
		uniqueItemId: string;
		oneDBarcode: string;
		productId: string;
		productName: string;
		productDescription: string;
		productCategory: string;
		destinationCountryCode: string;
		destinationCountryName: string;
		originCountryCode: string;
		originCountryName: string;
		lastEventCode: string;
		lastEventName: string;
		lastEventDateTime: string;
		lastEventLocationName: string;
		statusDescription: string;
		statusCategory: string;
		statusHelpText: string;
		summaryLine: string;
		internationalPostalProvider: InternationalPostalProvider;
	};
	signature: {
		recipientName: string;
		signatureDateTime: string;
		imageId: string;
	};
	estimatedDelivery: {
		date: string;
		startOfEstimatedWindow: string;
		endOfEstimatedWindow: string;
	};
	events: Event[];
	links: {
		summary: Link;
		signature: Link;
		redelivery: Link;
	};
}

// Define the structure of the API response
interface ApiResponse {
	rm: {
		mailPieces: MailPiece;
	};
}

// Mock data
const results: ApiResponse[] = [
	{
		rm: {
			mailPieces: {
				mailPieceId: '090367574000000FE1E1B',
				carrierShortName: 'RM',
				carrierFullName: 'Royal Mail Group Ltd',
				summary: {
					uniqueItemId: '090367574000000FE1E1B',
					oneDBarcode: 'FQ087430672GB',
					productId: 'SD2',
					productName: 'Special Delivery Guaranteed',
					productDescription: 'Our guaranteed next working day service with tracking and a signature on delivery',
					productCategory: 'NON-INTERNATIONAL',
					destinationCountryCode: 'GBR',
					destinationCountryName: 'United Kingdom of Great Britain and Northern Ireland',
					originCountryCode: 'GBR',
					originCountryName: 'United Kingdom of Great Britain and Northern Ireland',
					lastEventCode: 'EVNMI',
					lastEventName: 'Forwarded - Mis-sort',
					lastEventDateTime: '2016-10-20T10:04:00+01:00',
					lastEventLocationName: 'Stafford DO',
					statusDescription: "It's being redirected",
					statusCategory: 'IN TRANSIT',
					statusHelpText:
						'The item is in transit and a confirmation will be provided on delivery. For more information on levels of tracking by service, please see Sending Mail.',
					summaryLine: 'Item FQ087430672GB was forwarded to the Delivery Office on 2016-10-20.',
					internationalPostalProvider: {
						url: 'https://www.royalmail.com/track-your-item',
						title: 'Royal Mail Group Ltd',
						description: 'Royal Mail Group Ltd',
					},
				},
				signature: {
					recipientName: 'Simon',
					signatureDateTime: '2016-10-20T10:04:00+01:00',
					imageId: '001234',
				},
				estimatedDelivery: {
					date: '2017-02-20',
					startOfEstimatedWindow: '08:00:00+01:00',
					endOfEstimatedWindow: '11:00:00+01:00',
				},
				events: [
					{
						eventCode: 'EVNMI',
						eventName: 'Forwarded - Mis-sort',
						eventDateTime: '2016-10-20T10:04:00+01:00',
						locationName: 'Stafford DO',
					},
				],
				links: {
					summary: {
						href: '/mailpieces/v2/summary?mailPieceId=090367574000000FE1E1B',
						title: 'Summary',
						description: 'Get summary',
					},
					signature: {
						href: '/mailpieces/v2/090367574000000FE1E1B/signature',
						title: 'Signature',
						description: 'Get signature',
					},
					redelivery: {
						href: '/personal/receiving-mail/redelivery',
						title: 'Redelivery',
						description: 'Book a redelivery',
					},
				},
			},
		},
	},
];

// Filter by carrier name
const filterByCarrier = (carrier: string): ApiResponse | null => {
	return results.find((item) => item.rm.mailPieces.carrierShortName === carrier) || null;
};

// API handler
export default defineEventHandler((event) => {
	const query = getQuery(event) as { carrier?: string };

	// If carrier query param is present, filter results; otherwise, return null
	return query.carrier ? filterByCarrier(query.carrier) : [];
});
