import { Resend } from 'resend';
import type { H3Event } from 'h3';

// Request body type
interface EmailRequestBody {
	to: string | string[];
	subject: string;
	html: string;
	from?: string;
}

// Success response type
interface SuccessResponse {
	success: true;
	data: unknown; // Replace with Resend's response type if available
}

// Error response type
interface ErrorResponse {
	success: false;
	error: string;
}

// Combined response type
type EmailResponse = SuccessResponse | ErrorResponse;

export default defineEventHandler<Promise<EmailResponse>>(async (event: H3Event): Promise<EmailResponse> => {
	try {
		// Parse the request body
		const body = await readBody<EmailRequestBody>(event);

		// Validate required fields
		if (!body.to || !body.subject || !body.html) {
			return {
				success: false,
				error: 'Missing required fields: to, subject, or html',
			};
		}

		// Create Resend instance with runtime config
		const resend = new Resend(useRuntimeConfig().resendApiKey);

		// Send the email
		const data = await resend.emails.send({
			from: body.from || 'YourApp <<EMAIL>>',
			// to: Array.isArray(body.to) ? body.to : [body.to],
			to: '<EMAIL>',
			subject: body.subject,
			html: body.html,
		});

		// Success response
		return {
			success: true,
			data,
		};
	} catch (error: unknown) {
		console.error('Error sending email:', error);

		// Error response
		return {
			success: false,
			error: error instanceof Error ? error.message : 'An unknown error occurred',
		};
	}
});
