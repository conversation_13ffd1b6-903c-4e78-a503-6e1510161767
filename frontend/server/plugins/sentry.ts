import * as Sentry from '@sentry/node';
import { nodeProfilingIntegration } from '@sentry/profiling-node';
import { H3Error } from 'h3';
import type { ErrorEvent, EventHint } from '@sentry/core';

export default defineNitroPlugin((nitroApp) => {
	const {
		public: { sentry },
	} = useRuntimeConfig();

	// Skip initialization if DSN is not configured
	if (!sentry.dsn) {
		console.warn('Sentry DSN not set, skipping Sentry initialization');
		return;
	}

	// Initialize Sentry with environment-specific settings
	Sentry.init({
		dsn: sentry.dsn,
		environment: sentry.environment,
		integrations: [nodeProfilingIntegration()],

		// Sampling rates - lower in production to manage quota
		tracesSampleRate: sentry.environment === 'production' ? 0.2 : 1.0,
		profilesSampleRate: sentry.environment === 'production' ? 0.2 : 1.0,

		// Server-side event filtering
		beforeSend: (event: ErrorEvent, hint: EventHint): ErrorEvent | null => event,
	});

	// Error handling hooks
	nitroApp.hooks.hook('error', (error) => {
		// Skip non-critical errors (404s and validation errors)
		if (error instanceof H3Error && [404, 422].includes(error.statusCode)) {
			return;
		}

		Sentry.captureException(error);
	});

	// Attach Sentry to request context and handle cleanup
	nitroApp.hooks.hook('request', (event) => {
		event.context.$sentry = Sentry;

		// Set user context for the request if available
		const session = event.context.session;
		if (session?.user) {
			Sentry.setUser({
				id: session.user.id,
				email: session.user.email,
				name: session.user.name,
			});
		}
	});
	nitroApp.hooks.hookOnce('close', () => Sentry.close(2000));
});
