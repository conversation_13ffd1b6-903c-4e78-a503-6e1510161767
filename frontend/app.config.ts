export default defineAppConfig({
	ui: {
		colors: {
			primary: 'indigo',
			neutral: 'zinc',
		},
		// button: {
		// 	slots: {
		// 		base: ['cursor-pointer hover:opacity-100'],
		// 	},
		// 	variants: {
		// 		size: {
		// 			xl: {
		// 				base: '',
		// 			},
		// 		},
		// 	},
		// 	compoundVariants: [
		// 		{
		// 			color: 'primary',
		// 			variant: 'solid',
		// 			class: 'text-white',
		// 		},
		// 	],
		// },
		// separator: {
		// 	variants: {
		// 		color: {
		// 			neutral: {
		// 				border: 'border-[var(--ui-border-muted)]',
		// 			},
		// 		},
		// 	},
		// },
		// toast: {
		// 	slots: {
		// 		progress: 'hidden',
		// 	},
		// },
		// input: {
		// 	// slots: {
		// 	// 	base: ['placeholder:text-[var(--ui-text-primary-500)]'],
		// 	// },
		// 	variants: {
		// 		size: {
		// 			xs: {
		// 				base: 'px-2 py-1 text-xs gap-1',
		// 				leading: 'ps-2',
		// 				trailing: 'pe-2',
		// 				leadingIcon: 'size-4',
		// 				leadingAvatarSize: '3xs',
		// 				trailingIcon: 'size-4',
		// 			},
		// 			sm: {
		// 				base: 'px-2.5 py-1.5 text-xs gap-1.5',
		// 				leading: 'ps-2.5',
		// 				trailing: 'pe-2.5',
		// 				leadingIcon: 'size-4',
		// 				leadingAvatarSize: '3xs',
		// 				trailingIcon: 'size-4',
		// 			},
		// 			md: {
		// 				base: 'px-2.5 py-1.5 text-sm gap-1.5',
		// 				leading: 'ps-2.5',
		// 				trailing: 'pe-2.5',
		// 				leadingIcon: 'size-5',
		// 				leadingAvatarSize: '2xs',
		// 				trailingIcon: 'size-5',
		// 			},
		// 			lg: {
		// 				base: 'px-3.5 py-2.5 text-sm gap-2',
		// 				leading: 'ps-3',
		// 				trailing: 'pe-3',
		// 				leadingIcon: 'size-5',
		// 				leadingAvatarSize: '2xs',
		// 				trailingIcon: 'size-5',
		// 			},
		// 			xl: {
		// 				base: 'px-3 py-2 text-base gap-2',
		// 				leading: 'ps-3',
		// 				trailing: 'pe-3',
		// 				leadingIcon: 'size-6',
		// 				leadingAvatarSize: 'xs',
		// 				trailingIcon: 'size-6',
		// 			},
		// 		},
		// 		variant: {
		// 			outline: 'ring ring-inset ring-[var(--ui-border)]',
		// 			subtle: 'bg-[var(--ui-bg-muted)] ',
		// 		},
		// 	},
		// },
		// icons: {
		// 	arrowLeft: 'i-lucide-arrow-left',
		// 	arrowRight: 'i-lucide-arrow-right',
		// 	check: 'i-lucide-check',
		// 	chevronDoubleLeft: 'i-lucide-chevrons-left',
		// 	chevronDoubleRight: 'i-lucide-chevrons-right',
		// 	chevronDown: 'i-lucide-chevron-down',
		// 	chevronLeft: 'i-lucide-chevron-left',
		// 	chevronRight: 'i-lucide-chevron-right',
		// 	chevronUp: 'i-lucide-chevron-up',
		// 	close: 'i-lucide-x',
		// 	ellipsis: 'i-lucide-ellipsis',
		// 	external: 'i-lucide-arrow-up-right',
		// 	loading: 'i-lucide-refresh-cw',
		// 	minus: 'i-lucide-minus',
		// 	plus: 'i-lucide-plus',
		// 	search: 'i-lucide-search',
		// },
	},
});
