<template>
	<UApp>
		<NuxtLayout>
			<ErrorBoundary>
				<NuxtPage v-bind="$attrs" />
				false!
			</ErrorBoundary>
		</NuxtLayout>
	</UApp>
</template>
<script lang="ts" setup>
const runtimeConfig = useRuntimeConfig();
const route = useRoute();

/*
 * Base Meta
 */
useHead({
	titleTemplate: (titleChunk) => {
		return titleChunk ? `${runtimeConfig.public.env} Jobs: ${titleChunk}` : 'Jobs';
	},
	htmlAttrs: {
		lang: 'en',
		'data-page-id': computed(() => (route.meta.pageId as string) || ''),
	},
	meta: [
		{ name: 'description', content: 'base description' },
		{ name: 'keywords', content: 'base, description' },
		{ charset: 'viewport', content: 'utf-8' },
		{ name: 'viewport', content: 'width=device-width, initial-scale=1.0, maximum-scale=1' },
		{ name: 'theme-color', content: '#5560C8' },
	],
	//   link: [{ rel: "preconnect", href: "https://rsms.me" }],
	//   link: [{ rel: "stylesheet", href: "https://rsms.me/inter/inter.css" }],
});
</script>
