<template>
	<div>
		<!-- <div
			v-if="!session?.user.emailVerified && verifyBanner"
			class="bg-(--ui-bg-muted) px-2 flex py-2 items-center justify-center text-center text-sm">
			<div class="grow">Hello {{ session?.user.name }}, Please <strong>verify your email</strong> by clciking the link we have sent you.</div>

			<UButton
				@click="hideBanner"
				size="sm"
				color="neutral"
				variant="soft"
				class="ml-auto"
				icon="i-lucide-x" />
		</div> -->
		<!-- <Header /> -->
		<Header />
		<main class="p-8 flex flex-col">
			<slot />
		</main>
	</div>
</template>

<script lang="ts" setup>
import { useAuthSession } from '~/server/utils/auth-client';

const { data: session } = await useAuthSession(useFetch);

const verifyBanner = ref(true);
const hideBanner = () => {
	verifyBanner.value = false;
};
</script>
