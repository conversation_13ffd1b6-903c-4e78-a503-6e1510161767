# Test environment variables - EXAMPLE FILE
# Copy this file to .env.test and fill in your values
# .env.test should not be committed to Git

NODE_ENV=test
PORT=3333

# Sentry Configuration
SENTRY_ENVIRONMENT="test"
SENTRY_DSN=""

# Database
# Using Neon test branch - connection string is loaded from .env.test.local
# DATABASE_URL="postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"

# Auth
# These values need to be set in GitHub Secrets for CI
# For local development, you can add real values to .env.test.local (which is git-ignored)
GOOGLE_CLIENT_ID=""
GOOGLE_CLIENT_SECRET=""
TWITTER_CLIENT_ID=""
TWITTER_CLIENT_SECRET=""
BETTER_AUTH_SECRET=""
BETTER_AUTH_URL="http://localhost:3333"

# Email
RESEND_API_KEY=""
