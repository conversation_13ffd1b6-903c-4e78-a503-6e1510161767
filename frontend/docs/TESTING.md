## Test Structure

### Unit Tests

Unit tests are organized alongside the components they test:

```
components/
├── Button/
│   ├── Button.vue
│   └── Button.test.ts
├── Card/
│   ├── Card.vue
│   └── Card.test.ts
```

This co-location makes it easy to find tests and maintain them when components change.

### End-to-End Tests

End-to-end tests are located in the `tests/e2e` directory:

```
tests/
└── e2e/
    ├── index.spec.ts
    ├── auth.spec.ts
    └── ...
```

## Running Different Types of Tests

-   **Unit Tests**: `npm run test`
-   **E2E Tests**: `npm run test:e2e`
-   **E2E Tests with UI**: `npm run test:e2e:ui`
-   **E2E Tests in Headed Mode**: `npm run test:e2e:headed`
-   **E2E Tests in Debug Mode**: `npm run test:e2e:debug`
## Test Coverage

The project is configured to generate test coverage reports using Vitest's coverage tools. Coverage reports show how much of your code is covered by tests.

### Running Coverage Reports

```bash
# Generate coverage report
npm run test:coverage

# Generate coverage report with UI
npm run test:coverage:ui
```

### Coverage Configuration

Coverage is configured in `vitest.config.ts` with the following settings:

- **Provider**: v8 (Node.js built-in coverage)
- **Reporters**: text, JSON, and HTML
- **Thresholds**: 
  - Lines: 70%
  - Functions: 70%
  - Branches: 60%
  - Statements: 70%

### Coverage Exclusions

The following files and directories are excluded from coverage calculations:

- Test files (`*.test.ts`, `*.spec.ts`, `tests/`)
- Build and config files (`node_modules/`, `dist/`, `.nuxt/`, `*.config.ts`)
- Server and utility files (`server/db/migrations/`, `server/db/seed*.ts`, `scripts/`)
- Other non-testable files (`*.d.ts`, `types/`, `mocks/`, `public/`, `assets/`)

This ensures that coverage reports only include files that can and should be tested.
