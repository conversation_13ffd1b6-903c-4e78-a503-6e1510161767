<template>
	<button
		class="btn"
		:class="[variant, size]"
		:disabled="disabled"
		@click="$emit('click')">
		{{ label }}
	</button>
</template>

<script setup lang="ts">
defineProps({
	label: {
		type: String,
		required: true,
	},
	variant: {
		type: String,
		default: 'primary',
		validator: (value: string) => ['primary', 'secondary', 'danger'].includes(value),
	},
	size: {
		type: String,
		default: 'md',
		validator: (value: string) => ['sm', 'md', 'lg'].includes(value),
	},
	disabled: {
		type: Boolean,
		default: false,
	},
});

defineEmits(['click']);
</script>

<style scoped>
.btn {
	border-radius: 0.25rem;
	font-weight: 500;
	cursor: pointer;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	transition: all 0.2s ease;
}

.btn:disabled {
	opacity: 0.6;
	cursor: not-allowed;
}

/* Variants */
.primary {
	background-color: #3b82f6;
	color: white;
	border: 1px solid #3b82f6;
}

.primary:hover:not(:disabled) {
	background-color: #2563eb;
	border-color: #2563eb;
}

.secondary {
	background-color: white;
	color: #1f2937;
	border: 1px solid #d1d5db;
}

.secondary:hover:not(:disabled) {
	background-color: #f3f4f6;
}

.danger {
	background-color: #ef4444;
	color: white;
	border: 1px solid #ef4444;
}

.danger:hover:not(:disabled) {
	background-color: #dc2626;
	border-color: #dc2626;
}

/* Sizes */
.sm {
	padding: 0.375rem 0.75rem;
	font-size: 0.875rem;
}

.md {
	padding: 0.5rem 1rem;
	font-size: 1rem;
}

.lg {
	padding: 0.75rem 1.5rem;
	font-size: 1.125rem;
}
</style>
