<template>
	<!-- Render child content in the default slot -->
	<slot />
</template>

<script setup lang="ts">
import { shallowRef, onErrorCaptured, onBeforeUnmount } from 'vue';
import { useRouter, useToast, useNuxtApp } from '#imports';

// Types
interface ClearErrorOptions {
	redirect?: string;
}

// State management
const error = shallowRef<Error | null>(null);
const router = useRouter();
const handlers = new Set<() => void>();

/**
 * Clear current error and optionally redirect to a new route
 */
const clearError = async ({ redirect }: ClearErrorOptions = {}) => {
	if (redirect) await router.push(redirect);
	error.value = null;
};

/**
 * Global error handler that captures errors and reports them
 */
onErrorCaptured((err: Error) => {
	error.value = err;

	// Show error toast
	const toast = useToast();
	toast.add({
		id: 'error',
		title: 'An error occurred',
		description: err.message,
		color: 'error',
	});

	// Log to Sentry if available
	const nuxtApp = useNuxtApp();
	if (nuxtApp.$sentry) {
		nuxtApp.$sentry.captureException(err);
	}

	return false; // Prevent error propagation
});

// Register route change handler
router.beforeEach(() => {
	handlers.forEach((handler) => handler());
});

// Register cleanup handler
handlers.add(clearError);

// Cleanup on unmount
onBeforeUnmount(() => {
	handlers.clear();
});

// Expose for testing
defineExpose({
	error,
	handlers: {
		onError: (err: Error) => {
			error.value = err;
			const toast = useToast();
			toast.add({
				id: 'error',
				title: 'An error occurred',
				description: err.message,
				color: 'error',
			});
		},
		onRouteChange: () => clearError({ redirect: '/' }),
	},
});
</script>
