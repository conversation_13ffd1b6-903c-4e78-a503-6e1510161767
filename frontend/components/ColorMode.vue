<template>
	{{ colorMode.value }}
	<button
		type="button"
		class="hs-dark-mode hs-dark-mode-active:hidden inline-flex items-center gap-x-2 py-2 px-3 bg-white/10 rounded-full text-sm text-white hover:bg-white/20 focus:outline-none focus:bg-white/20"
		@click="toggleColorMode">
		<svg
			class="shrink-0 size-4"
			xmlns="http://www.w3.org/2000/svg"
			width="24"
			height="24"
			viewBox="0 0 24 24"
			fill="none"
			stroke="currentColor"
			stroke-width="2"
			stroke-linecap="round"
			stroke-linejoin="round">
			<path d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z"></path>
		</svg>
		Dark
	</button>
</template>
<script setup lang="ts">
const colorMode = useColorMode();

const toggleColorMode = () => {
	colorMode.preference = colorMode.value === 'dark' ? 'light' : 'dark';
	console.log(colorMode.value);
};
</script>
