<template>
	<!-- Reusable navigation template definition -->
	<!-- The scoped slot provides:
		 - orientation: the menu's orientation (horizontal or vertical)
		 - navItems: an array of NavigationMenuItem objects -->
	<DefineNavTemplate v-slot="{ orientation }">
		<UNavigationMenu
			:items="navItems"
			:orientation="orientation"
			:ui="{ link: 'text-md font-300' }"
			class="w-full justify-center" />
	</DefineNavTemplate>

	<DefineAccountTemplate>
		<UDropdownMenu :items="accountItems">
			<UButton variant="ghost">
				<UAvatar src="https://github.com/benjamincanac.png" />
			</UButton>
		</UDropdownMenu>
	</DefineAccountTemplate>

	<!-- Reusable authentication template definition -->
	<DefineAuthTemplate>
		<!-- Show Sign In button if user is not logged in -->
		<UButton
			to="/sign-up"
			variant="outline"
			color="neutral">
			Sign Up
		</UButton>
		<UButton
			to="/sign-in"
			color="neutral">
			Sign In
		</UButton>
		<div v-if="session">
			{{ session }}
			<UButton
				class="px-5 py-3 border border-slate-400 rounded-lg"
				@click="handleSignOut">
				Logout
			</UButton>
		</div>
	</DefineAuthTemplate>

	<header class="flex items-center px-6 border-b border-(--ui-border) h-(--ui-header-height) pointer-events-auto">
		<!-- Left Section: Branding and Badge -->
		<Logo />

		<!-- Center Section: Navigation Menu for Larger Screens -->
		<div class="flex-grow hidden md:flex justify-center">
			<ReuseNavTemplate orientation="horizontal" />
		</div>

		<!-- Right Section: Account Actions -->
		<div class="flex ml-auto md:ml-0 items-center">
			<!-- Show Sign In button if user is not logged in -->

			<div class="hidden md:flex gap-2">
				<ReuseAuthTemplate v-if="!session" />
				<ReuseAccountTemplate v-if="session" />
			</div>

			<UButton
				@click="toggleNavStatus"
				class="md:hidden"
				:icon="navStatus ? 'i-lucide-x' : 'i-lucide-align-justify'"
				color="neutral"
				variant="ghost" />

			<!-- Modal for Mobile Navigation -->
			<UModal
				class="md:hidden"
				v-model:open="navStatus"
				fullscreen
				:overlay="false"
				:dismissible="true"
				:transition="false"
				:close="false"
				:ui="{ footer: 'justify-end', content: 'menu-modal' }">
				<template #content>
					<div class="flex flex-col h-full">
						<div class="flex items-center px-6 h-(--ui-header-height) border-b border-(--ui-border)">
							<Logo />
							<UButton
								@click="toggleNavStatus"
								class="ml-auto"
								:icon="navStatus ? 'i-lucide-x' : 'i-lucide-align-justify'"
								color="neutral"
								variant="ghost" />
						</div>
						<div class="p-4 flex-1">
							<ReuseNavTemplate orientation="vertical" />
						</div>
						<div class="justify-end p-4">
							<ReuseAuthTemplate v-if="!session" />
							footer
							<ReuseAccountTemplate v-if="session" />
						</div>
					</div>
				</template>
			</UModal>
		</div>
	</header>
</template>

<script lang="ts" setup>
// --- Imports ---
import { ref } from 'vue';
import { createReusableTemplate } from '@vueuse/core';
import { useRouter, useRoute } from 'vue-router';
import { useAuthSession, signOut } from '~/server/utils/auth-client';

// --- Reusable Template Setup ---
// The reusable template for the navigation menu.
// (No explicit generic types are needed here if the inferred types work as expected.)
const [DefineNavTemplate, ReuseNavTemplate] = createReusableTemplate();
const [DefineAuthTemplate, ReuseAuthTemplate] = createReusableTemplate();
const [DefineAccountTemplate, ReuseAccountTemplate] = createReusableTemplate();

const navStatus = ref(false);

// --- State Variables ---
const open = ref(false); // Controls the modal's visibility

const toggleNavStatus = () => {
	navStatus.value = !navStatus.value;
};

// --- Router and Session ---
const router = useRouter();
const route = useRoute();
const { data: session } = await useAuthSession(useFetch);

// --- Functions ---
const handleSignOut = async () => {
	await signOut();
	router.push('/login');
};

const closeModal = () => {
	open.value = false;
};

// --- Dropdown Items ---
// Renamed from "items" to "accountItems" for clarity.
// Structured as an array of arrays to match nuxtui's expected shape.
const accountItems = ref([
	[
		{
			label: 'My account',
			avatar: {
				src: 'https://github.com/benjamincanac.png',
			},
		},
	],
	[
		{
			label: 'Profile',
			icon: 'i-heroicons-user',
		},
		{
			label: 'Billing',
			icon: 'i-heroicons-credit-card',
		},
		{
			label: 'Settings',
			icon: 'i-heroicons-cog',
		},
	],
	[
		{
			label: 'Team',
			icon: 'i-heroicons-users',
		},
	],
]);

// --- Navigation Menu Items ---
// These items are passed to the reusable navigation template.
const navItems = ref([{ label: 'Track Parcel' }, { label: 'Couriers' }, { label: 'About' }]);

const colorMode = useColorMode();

const isDark = computed({
	get() {
		return colorMode.value === 'dark';
	},
	set() {
		colorMode.preference = colorMode.value === 'dark' ? 'light' : 'dark';
	},
});
</script>

<style></style>
