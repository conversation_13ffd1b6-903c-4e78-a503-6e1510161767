import * as Sentry from '@sentry/vue';
import { Event, EventHint } from '@sentry/types';

declare module '#app' {
	interface NuxtApp {
		$sentry: typeof Sentry;
	}
}

// Extend the Window interface to include our custom property
declare global {
	interface Window {
		__sentryEvents?: number[];
	}
}

// Define a type for our rate limiting configuration
export interface SentryRateLimitConfig {
	rateLimit: number;
	timeWindow: number;
}

// Define a type for our Sentry configuration
export interface SentryConfig {
	dsn: string | undefined;
	environment: string;
	rateLimitConfig?: SentryRateLimitConfig;
}
