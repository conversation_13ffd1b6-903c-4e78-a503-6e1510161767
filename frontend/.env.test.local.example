# Test environment local overrides
# Copy this file to .env.test.local and fill in your values
# This file is git-ignored and should not be committed

# Database - Use your Neon test branch connection string
DATABASE_URL="postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"

# Sentry Configuration
SENTRY_ENVIRONMENT="test"
SENTRY_DSN="your-sentry-dsn"

# Auth
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
TWITTER_CLIENT_ID="your-twitter-client-id"
TWITTER_CLIENT_SECRET="your-twitter-client-secret"
BETTER_AUTH_SECRET="your-better-auth-secret"
BETTER_AUTH_URL="http://localhost:3333"

# Email
RESEND_API_KEY="your-resend-api-key"
