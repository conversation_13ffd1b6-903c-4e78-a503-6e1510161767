import { authClient, useAuthSession } from '~/server/utils/auth-client';

export default defineNuxtRouteMiddleware(async (to) => {
	// Check if the user is navigating to the app route
	const isUserNavigatingToTheApp = to.path.startsWith('/protected');
	const { data: session } = await useAuthSession(useFetch);

	if (session.value?.user) {
		if (!session.value.user.emailVerified && to.path === '/protected') {
			return navigateTo('/verify');
		}

		// if (session.value.user.emailVerified && to.path === '/verify') {
		// 	return navigateTo('/');
		// }

		if ((!session.value?.user && to.path === '/sign-in') || to.path === '/sign-up') {
			return navigateTo('/');
		}
	}

	if (isUserNavigatingToTheApp && !session.value?.user) {
		return navigateTo('/sign-in');
	}
});
