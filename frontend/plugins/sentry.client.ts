import * as Sentry from '@sentry/vue';
import type { ErrorEvent, EventHint } from '@sentry/core';
import type { SentryRateLimitConfig } from '~/types/sentry';
import { useAuthSession } from '~/server/utils/auth-client';

export default defineNuxtPlugin((nuxtApp) => {
	const {
		public: { sentry },
	} = useRuntimeConfig();
	const router = useRouter();

	// Skip initialization if DSN is not configured
	if (!sentry.dsn) {
		return;
	}

	// Rate limiting configuration
	const rateLimitConfig: SentryRateLimitConfig = {
		rateLimit: 10,
		timeWindow: 60 * 1000, // 1 minute in milliseconds
	};

	// Rate limiting implementation
	const beforeSend = (event: ErrorEvent, hint: EventHint): ErrorEvent | null => {
		const now = Date.now();
		const events = window.__sentryEvents || [];
		const recentEvents = events.filter((timestamp) => now - timestamp < rateLimitConfig.timeWindow);

		if (recentEvents.length >= rateLimitConfig.rateLimit) {
			console.warn('Sentry rate limit reached, dropping event');
			return null;
		}

		recentEvents.push(now);
		window.__sentryEvents = recentEvents;

		// Add user context if available
		const session = useAuthSession();
		if (session.value?.data?.user) {
			event.user = {
				id: session.value.data.user.id,
				email: session.value.data.user.email,
				name: session.value.data.user.name,
			};
		}

		return event;
	};

	// Initialize Sentry with environment-specific settings
	Sentry.init({
		app: nuxtApp.vueApp,
		dsn: sentry.dsn,
		environment: sentry.environment,

		// Integrations
		integrations: [
			Sentry.browserTracingIntegration({ router }),
			Sentry.replayIntegration({
				maskAllText: false,
				blockAllMedia: false,
			}),
		],

		// Sampling rates - lower in production to manage quota
		tracesSampleRate: sentry.environment === 'production' ? 0.2 : 1.0,
		replaysSessionSampleRate: sentry.environment === 'production' ? 0.1 : 1.0,
		replaysOnErrorSampleRate: 1.0, // Always capture error replays

		// Rate limiting
		beforeSend,

		// Data volume limits
		maxBreadcrumbs: 50,
		maxValueLength: 1000,

		// URL filtering for distributed tracing
		tracePropagationTargets: ['localhost', 'https://your-server.com'],
	});

	nuxtApp.provide('sentry', Sentry);
});
