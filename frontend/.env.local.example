# Local development environment variables - EXAMPLE FILE
# Copy this file to .env.local and fill in your values
# .env.local is git-ignored and should never be committed

# Environment
NODE_ENV=development

# Database
DATABASE_URL="postgresql://postgres:password123@localhost:5432/fantasyfix"

# Sentry Configuration
SENTRY_ENVIRONMENT="development"
SENTRY_DSN=""

# Auth
GOOGLE_CLIENT_ID=""
GOOGLE_CLIENT_SECRET=""
TWITTER_CLIENT_ID=""
TWITTER_CLIENT_SECRET=""
BETTER_AUTH_SECRET=""
BETTER_AUTH_URL="http://localhost:3000"

# Email
RESEND_API_KEY=""
