# Multi-stage Dockerfile for Nuxt application

# ---- Base Node ----
FROM node:20-alpine AS base
WORKDIR /app
ENV NODE_ENV=production

# ---- Builder ----
FROM base AS builder
WORKDIR /app
# Set NODE_ENV to development for the build stage to ensure dev dependencies are installed
ENV NODE_ENV=development
# Copy package files and install ALL dependencies (including dev dependencies)
COPY package*.json ./
COPY frontend/package*.json ./frontend/
RUN npm ci --prefer-offline --no-audit
# Copy source code and build
COPY frontend/ ./frontend/
WORKDIR /app/frontend
# Set NODE_ENV back to production for the build process
ENV NODE_ENV=production
RUN npm run build

# ---- Production ----
FROM node:20-alpine AS production
WORKDIR /app
ENV NODE_ENV=production

# Install tsx for running TypeScript migration scripts
RUN npm install -g tsx

# Copy the built application
COPY --from=builder /app/frontend/.output ./.output

# Copy migration files and scripts
COPY --from=builder /app/frontend/server/db/migrations ./server/db/migrations
COPY --from=builder /app/frontend/server/utils/migrations.ts ./server/utils/migrations.ts
COPY --from=builder /app/frontend/server/utils/run-migrations.ts ./server/utils/run-migrations.ts
COPY --from=builder /app/frontend/package.json ./package.json

# Create startup script that runs migrations then starts the app
RUN echo '#!/bin/sh\necho "Running database migrations..."\ntsx server/utils/run-migrations.ts --production\necho "Starting application..."\nnode .output/server/index.mjs' > /app/start.sh
RUN chmod +x /app/start.sh

# Expose the port the app runs on
EXPOSE 3000

# Command to run migrations and then the application
CMD ["/app/start.sh"]
