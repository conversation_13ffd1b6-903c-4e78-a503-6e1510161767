# Production environment variables - EXAMPLE FILE
# This file serves as a template for the variables needed in production
# DO NOT add actual secrets to this file as it will be committed to the repository
# In Coolify, set these environment variables directly in the dashboard

# Core environment variables
NODE_ENV=production

# Database
DATABASE_URL=your-neon-database-url

# Auth configuration
BETTER_AUTH_SECRET=your-auth-secret
BETTER_AUTH_URL=https://your-domain.example.com
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
TWITTER_CLIENT_ID=your-twitter-client-id
TWITTER_CLIENT_SECRET=your-twitter-client-secret

# Services
RESEND_API_KEY=your-resend-api-key
SENTRY_DSN=your-sentry-dsn
SENTRY_ENVIRONMENT=production
